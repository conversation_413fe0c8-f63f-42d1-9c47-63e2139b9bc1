using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SendGrid;
using SendGrid.Helpers.Mail;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Services
{
    // Email data record types for simplified method signatures
    public record PasswordResetEmailData(string ToEmail, string ResetToken, string VerificationCode);
    public record InvitationEmailData(string ToEmail, string InvitationToken, string VerificationCode, string ApplicationName, string FirstName, string LastName);
    public record AccountCreatedEmailData(string ToEmail, string FirstName, string ApplicationName);
    public record PasswordExpirationEmailData(string ToEmail, string ApplicationName, int DaysUntilExpiration, bool IsAbsent = false);
    public record PasswordExpiredEmailData(string ToEmail, string ApplicationName);

    public class EmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly ISendGridClient? _sendGridClient;
        private readonly string _fromEmail;
        private readonly string _resetBaseUrl;
        private readonly string _registrationBaseUrl;
        private readonly string _applicationName;
        private readonly string _fromName;
        private readonly string _passwordResetTemplateId;
        private readonly string _passwordChangedTemplateId;
        private readonly string _userInvitationTemplateId;
        private readonly string _accountCreatedTemplateId;
        private readonly string _passwordExpirationTemplateId;
        private readonly string _passwordExpiredTemplateId;

        public EmailService(ILogger<EmailService> logger, IOptions<SendGridOptions> sendGridOptions, IOptions<ApplicationOptions> applicationOptions, IOptions<PasswordResetOptions> passwordResetOptions, IOptions<AccountRegistrationOptions> accountRegistrationOptions)
        {
            _logger = logger;
            var sendGridOpts = sendGridOptions.Value;
            var appOpts = applicationOptions.Value;
            var passwordResetOpts = passwordResetOptions.Value;
            var accountRegistrationOpts = accountRegistrationOptions.Value;

            // Handle missing SendGrid configuration gracefully
            if (string.IsNullOrEmpty(sendGridOpts.ApiKey))
            {
                _logger.LogError("SendGrid:ApiKey is missing. Email functionality will be disabled. Configure your SendGrid API key in application settings or Azure Key Vault.");
                _sendGridClient = null;
            }
            else
            {
                try
                {
                    _sendGridClient = new SendGridClient(sendGridOpts.ApiKey);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to create SendGrid client. Email functionality will be disabled.");
                    _sendGridClient = null;
                }
            }

            // Set configuration values with defaults for missing values
            _fromEmail = sendGridOpts.FromEmail ?? "<EMAIL>";
            _resetBaseUrl = passwordResetOpts.BaseUrl ?? "https://example.com/password-reset";
            _registrationBaseUrl = accountRegistrationOpts.BaseUrl ?? "https://example.com/registration";

            // Set template IDs with validation - use placeholder if missing
            _passwordResetTemplateId = ValidateTemplateIdGraceful(sendGridOpts.PasswordResetTemplateId, "SendGrid:PasswordResetTemplateId");
            _passwordChangedTemplateId = ValidateTemplateIdGraceful(sendGridOpts.PasswordChangedTemplateId, "SendGrid:PasswordChangedTemplateId");
            _userInvitationTemplateId = ValidateTemplateIdGraceful(sendGridOpts.UserInvitationTemplateId, "SendGrid:UserInvitationTemplateId");
            _accountCreatedTemplateId = ValidateTemplateIdGraceful(sendGridOpts.AccountCreatedTemplateId, "SendGrid:AccountCreatedTemplateId");
            _passwordExpirationTemplateId = ValidateTemplateIdGraceful(sendGridOpts.PasswordExpirationTemplateId, "SendGrid:PasswordExpirationTemplateId");
            _passwordExpiredTemplateId = ValidateTemplateIdGraceful(sendGridOpts.PasswordExpiredTemplateId, "SendGrid:PasswordExpiredTemplateId");

            _applicationName = "Password Reset Service";
            _fromName = "Password Reset Service";

            // Log warning if email service is not fully configured
            if (_sendGridClient == null || _fromEmail == "<EMAIL>")
            {
                _logger.LogWarning("Email service is not fully configured. Email notifications will be disabled or may fail.");
            }
        }

        /// <summary>
        /// Validates a SendGrid template ID and throws an exception if invalid.
        /// </summary>
        private static string ValidateTemplateId(string? templateId, string configurationKey)
        {
            if (string.IsNullOrEmpty(templateId))
            {
                throw new InvalidOperationException($"{configurationKey} is required.");
            }
            return templateId;
        }

        /// <summary>
        /// Validates a SendGrid template ID gracefully, returning a placeholder if missing.
        /// </summary>
        private string ValidateTemplateIdGraceful(string? templateId, string configurationKey)
        {
            if (string.IsNullOrEmpty(templateId))
            {
                _logger.LogError("{ConfigurationKey} is missing. Email templates will use placeholder values.", configurationKey);
                return "d-placeholder-template-id";
            }
            return templateId;
        }

        private async Task<bool> SendEmailWithTemplate(string toEmail, string templateId, object templateData, string emailType, string correlationId, CancellationToken cancellationToken = default)
        {
            try
            {
                // Check if SendGrid client is available
                if (_sendGridClient == null)
                {
                    _logger.LogError("Cannot send {EmailType} email to {ToEmail}: SendGrid client is not configured. Correlation ID: {CorrelationId}", emailType, toEmail, correlationId);
                    return false;
                }

                var from = new EmailAddress(_fromEmail, _fromName);
                var to = new EmailAddress(toEmail);

                var message = new SendGridMessage()
                {
                    From = from,
                    TemplateId = templateId
                };
                message.AddTo(to);
                message.SetTemplateData(templateData);
                message.AddCustomArg("X-Correlation-ID", correlationId);
                message.AddCustomArg("X-Email-Type", emailType);
                message.AddCustomArg("X-Application-Name", _applicationName);

                var response = await _sendGridClient.SendEmailAsync(message, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    var errorMessage = response.Body != null ? await response.Body.ReadAsStringAsync() : "Unknown error";
                    _logger.LogError("Failed to send {EmailType} email to {Email}. StatusCode: {StatusCode}, Error: {Error} [CorrelationId: {CorrelationId}]",
                        emailType, toEmail, response.StatusCode, errorMessage, correlationId);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending {EmailType} email to {Email} [CorrelationId: {CorrelationId}]", emailType, toEmail, correlationId);
                return false;
            }
        }

        public async Task<bool> SendPasswordResetEmailAsync(PasswordResetEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var resetLink = $"{_resetBaseUrl}?token={data.ResetToken}";
            var templateData = new
            {
                resetLink = resetLink,
                verificationCode = data.VerificationCode,
                applicationName = _applicationName
            };

            return await SendEmailWithTemplate(data.ToEmail, _passwordResetTemplateId, templateData, "passwordReset", correlationId, cancellationToken);
        }

        public async Task<bool> SendPasswordChangedNotificationAsync(string toEmail, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateData = new
            {
                applicationName = _applicationName,
                email = toEmail,
                changeDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                changeTime = DateTime.UtcNow.ToString("HH:mm"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(toEmail, _passwordChangedTemplateId, templateData, "passwordChanged", correlationId, cancellationToken);
        }

        public async Task<bool> SendUserInvitationEmailAsync(InvitationEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var baseUrl = _registrationBaseUrl.TrimEnd('/');
            var invitationLink = $"{baseUrl}?token={data.InvitationToken}";

            var templateData = new
            {
                invitationLink = invitationLink,
                verificationCode = data.VerificationCode,
                applicationName = data.ApplicationName,
                firstName = data.FirstName,
                lastName = data.LastName,
                fullName = $"{data.FirstName} {data.LastName}"
            };

            return await SendEmailWithTemplate(data.ToEmail, _userInvitationTemplateId, templateData, "invitation", correlationId, cancellationToken);
        }

        /// <summary>
        /// Extracts the website URL from the reset base URL by removing the password reset path.
        /// </summary>
        private string GetWebsiteUrl()
        {
            return _resetBaseUrl.Replace("/Custom-Password-Reset/", "").TrimEnd('/');
        }

        public async Task<bool> SendAccountCreatedNotificationAsync(AccountCreatedEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateData = new
            {
                firstName = data.FirstName,
                applicationName = data.ApplicationName,
                email = data.ToEmail,
                creationDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                creationTime = DateTime.UtcNow.ToString("HH:mm"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(data.ToEmail, _accountCreatedTemplateId, templateData, "accountCreated", correlationId, cancellationToken);
        }

        public async Task<bool> SendPasswordExpirationNotificationAsync(PasswordExpirationEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var websiteUrl = GetWebsiteUrl();
            var forgotPasswordUrl = $"{websiteUrl}/Custom-Password-Reset/";

            var templateData = new
            {
                applicationName = data.ApplicationName,
                daysUntilExpiration = data.DaysUntilExpiration,
                websiteUrl = websiteUrl,
                forgotPasswordUrl = forgotPasswordUrl,
                email = data.ToEmail,
                notificationDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                correlationId = correlationId,
                absenceWarning = data.IsAbsent // Flag for template to show absence-specific messaging
            };

            return await SendEmailWithTemplate(data.ToEmail, _passwordExpirationTemplateId, templateData, "passwordExpiration", correlationId, cancellationToken);
        }

        public async Task<bool> SendPasswordExpiredNotificationAsync(PasswordExpiredEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var websiteUrl = GetWebsiteUrl();
            var forgotPasswordUrl = $"{websiteUrl}/Custom-Password-Reset/";

            var templateData = new
            {
                applicationName = data.ApplicationName,
                websiteUrl = websiteUrl,
                forgotPasswordUrl = forgotPasswordUrl,
                email = data.ToEmail,
                expiredDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(data.ToEmail, _passwordExpiredTemplateId, templateData, "passwordExpired", correlationId, cancellationToken);
        }
    }
}
