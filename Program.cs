using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Azure.Storage.Blobs;
using Azure.Identity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Graph;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using System.Text.Json;
using Microsoft.Azure.Functions.Worker;


var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureAppConfiguration((context, builder) =>
    {
        builder.AddEnvironmentVariables()
               .AddUserSecrets<Program>();

        var settings = builder.Build();

        var keyVaultUrl = settings.GetValue<string>("KeyVaultUrl");
        if (!string.IsNullOrEmpty(keyVaultUrl))
        {
            try
            {
                builder.AddAzureKeyVault(
                    new Uri(keyVaultUrl),
                    new DefaultAzureCredential(
                        new DefaultAzureCredentialOptions
                        {
                            ManagedIdentityClientId = settings.GetValue<string>("UserAssignedClientId")
                        }));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"WARNING: Failed to configure Key Vault: {ex.Message}. Continuing without Key Vault integration.");
            }
        }
    })
    .ConfigureServices((context, services) =>
    {
        var configuration = context.Configuration;

        services.AddApplicationInsightsTelemetryWorkerService();
        services.ConfigureFunctionsApplicationInsights();

        services.AddMemoryCache();

        // Validate required configuration - fail fast if anything is missing
        ConfigurationValidator.ValidateRequiredConfiguration(configuration);

        // Configure service-specific options with their respective sections
        services.Configure<SendGridOptions>(configuration.GetSection(SendGridOptions.SectionName));
        services.Configure<EntraExternalIDOptions>(configuration.GetSection(EntraExternalIDOptions.SectionName));
        services.Configure<PasswordHistoryOptions>(configuration.GetSection(PasswordHistoryOptions.SectionName));
        services.Configure<RateLimitOptions>(configuration.GetSection(RateLimitOptions.SectionName));
        services.Configure<PasswordResetOptions>(configuration.GetSection(PasswordResetOptions.SectionName));
        services.Configure<AccountRegistrationOptions>(configuration.GetSection(AccountRegistrationOptions.SectionName));
        services.Configure<InvitationOptions>(configuration.GetSection(InvitationOptions.SectionName));

        // Configure application options from direct environment variables
        services.Configure<ApplicationOptions>(options =>
        {
            options.ApplicationName = configuration["ApplicationName"] ?? string.Empty;
            options.KeyVaultUrl = configuration["KeyVaultUrl"] ?? string.Empty;
            options.PasswordExpirationDays = configuration.GetValue<int>("PASSWORD_EXPIRATION_DAYS", 90);
            options.PasswordWarningDays = configuration.GetValue<int>("PASSWORD_WARNING_DAYS", 15);
            options.InvitationTokenExpirationDays = configuration.GetValue<int>("INVITATION_TOKEN_EXPIRATION_DAYS", 45);
        });

        services.Configure<StorageOptions>(options =>
        {
            options.ConnectionString = configuration["AzureWebJobsStorage"] ?? configuration["Storage:ConnectionString"] ?? string.Empty;
        });
        
        // JSON options
        services.AddSingleton<JsonSerializerOptions>(provider => new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            WriteIndented = false
        });

        // HTTP client
        services.AddHttpClient("default")
                .AddStandardResilienceHandler(options =>
                {
                    options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(30);
                    options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(15);
                });

        
        // Blob storage client
        services.AddSingleton<BlobServiceClient>(serviceProvider =>
        {
            var storageOptions = serviceProvider.GetRequiredService<IOptions<StorageOptions>>();
            var connectionString = storageOptions.Value.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                var logger = serviceProvider.GetRequiredService<ILogger<BlobServiceClient>>();
                logger.LogError("Storage connection string is missing. Password history functionality will be disabled. Configure AzureWebJobsStorage or Storage:ConnectionString.");

                // Return a client with a placeholder connection string that will fail gracefully at runtime
                return new BlobServiceClient("DefaultEndpointsProtocol=https;AccountName=placeholder;AccountKey=placeholder;EndpointSuffix=core.windows.net");
            }

            return new BlobServiceClient(connectionString);
        });

        // Graph API client
        services.AddScoped<GraphServiceClient>(serviceProvider =>
        {
            var entraOptions = serviceProvider.GetRequiredService<IOptions<EntraExternalIDOptions>>();
            var options = entraOptions.Value;
            var logger = serviceProvider.GetRequiredService<ILogger<GraphServiceClient>>();

            if (string.IsNullOrEmpty(options.ClientId) || string.IsNullOrEmpty(options.ClientSecret) || string.IsNullOrEmpty(options.TenantId))
            {
                logger.LogError("Entra External ID configuration is incomplete. Authentication functions will be disabled. Configure EntraExternalID:ClientId, ClientSecret, and TenantId.");

                // Return null - services will need to check for null and handle gracefully
                return null!;
            }

            try
            {
                var credential = new ClientSecretCredential(options.TenantId, options.ClientId, options.ClientSecret);
                return new GraphServiceClient(credential);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create GraphServiceClient with provided credentials. Authentication functions will be disabled.");
                return null!;
            }
        });
        
        // Email service
        services.AddSingleton<EmailService>(serviceProvider =>
        {
            var logger = serviceProvider.GetRequiredService<ILogger<EmailService>>();
            var sendGridOptions = serviceProvider.GetRequiredService<IOptions<SendGridOptions>>();
            var appOptions = serviceProvider.GetRequiredService<IOptions<ApplicationOptions>>();
            var passwordResetOptions = serviceProvider.GetRequiredService<IOptions<PasswordResetOptions>>();
            var accountRegistrationOptions = serviceProvider.GetRequiredService<IOptions<AccountRegistrationOptions>>();

            return new EmailService(logger, sendGridOptions, appOptions, passwordResetOptions, accountRegistrationOptions);
        });
        
        // Core services
        services.AddSingleton<PasswordHistoryService>();
        services.AddSingleton<RateLimitService>();
        services.AddSingleton<ResetTokenManager>();
        services.AddSingleton<InvitationTokenManager>();

        // Function classes
        
        services.AddScoped<PasswordHistoryValidator.PasswordFunction>();
        services.AddScoped<PasswordHistoryValidator.UtilityFunction>();
        services.AddScoped<PasswordHistoryValidator.InvitationFunction>();
        services.AddScoped<PasswordHistoryValidator.RegistrationFunction>();
    })
    .Build();

host.Run();
